using System;
using Microsoft.Extensions.DependencyInjection;
using HotPreview.SharedModel.Protocol;

namespace HotPreview.SharedModel.App;

public class PreviewClassReflection : PreviewReflection
{
    private readonly bool _isAutoGenerated;

    public PreviewClassReflection(PreviewAttribute previewAttribute, Type type) : base(previewAttribute)
    {
        Type = type;
    }

    public PreviewClassReflection(Type type, bool isAutoGenerated) : base(type)
    {
        Type = type;
        _isAutoGenerated = isAutoGenerated;
    }

    public Type Type { get; }

    public override object Create()
    {
        IServiceProvider? serviceProvider = PreviewApplication.GetInstance().ServiceProvider;
        if (serviceProvider is not null)
        {
            return ActivatorUtilities.CreateInstance(serviceProvider, Type);
        }
        else
        {
            return Activator.CreateInstance(Type);
        }
    }

    public override Type? DefaultUIComponentType => null;

    public override string Name => Type.FullName;

    public override bool IsAutoGenerated => _isAutoGenerated;

    public override string GetPreviewTypeInfo() => PreviewTypeInfo.Class;
}
